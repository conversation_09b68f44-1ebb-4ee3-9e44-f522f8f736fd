# All ubuntu-18.04 jobs should run successfully in an environment
# setup by the scripts/ci/setup/build-environment.yml task
# "Install basic packages to build QEMU on Ubuntu 18.04/20.04"

ubuntu-18.04-s390x-all-linux-static:
 needs: []
 stage: build
 tags:
 - ubuntu_18.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
 - if: "$S390X_RUNNER_AVAILABLE"
 script:
 # --disable-libssh is needed because of https://bugs.launchpad.net/qemu/+bug/1838763
 # --disable-glusterfs is needed because there's no static version of those libs in distro supplied packages
 - mkdir build
 - cd build
 - ../configure --enable-debug --static --disable-system --disable-glusterfs --disable-libssh
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check V=1
 - make --output-sync -j`nproc` check-tcg V=1

ubuntu-18.04-s390x-all:
 needs: []
 stage: build
 tags:
 - ubuntu_18.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
 - if: "$S390X_RUNNER_AVAILABLE"
 script:
 - mkdir build
 - cd build
 - ../configure --disable-libssh
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check V=1

ubuntu-18.04-s390x-alldbg:
 needs: []
 stage: build
 tags:
 - ubuntu_18.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$S390X_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --enable-debug --disable-libssh
 - make clean
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check V=1

ubuntu-18.04-s390x-clang:
 needs: []
 stage: build
 tags:
 - ubuntu_18.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$S390X_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --disable-libssh --cc=clang --cxx=clang++ --enable-sanitizers
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check V=1

ubuntu-18.04-s390x-tci:
 needs: []
 stage: build
 tags:
 - ubuntu_18.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$S390X_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --disable-libssh --enable-tcg-interpreter
 - make --output-sync -j`nproc`

ubuntu-18.04-s390x-notcg:
 needs: []
 stage: build
 tags:
 - ubuntu_18.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$S390X_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --disable-libssh --disable-tcg
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check V=1
