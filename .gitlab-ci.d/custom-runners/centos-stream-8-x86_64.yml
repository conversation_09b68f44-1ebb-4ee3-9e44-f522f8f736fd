centos-stream-8-x86_64:
 allow_failure: true
 needs: []
 stage: build
 tags:
 - centos_stream_8
 - x86_64
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
 - if: "$CENTOS_STREAM_8_x86_64_RUNNER_AVAILABLE"
 artifacts:
   name: "$CI_JOB_NAME-$CI_COMMIT_REF_SLUG"
   when: on_failure
   expire_in: 7 days
   paths:
     - build/tests/results/latest/results.xml
     - build/tests/results/latest/test-results
   reports:
     junit: build/tests/results/latest/results.xml
 before_script:
 - JOBS=$(expr $(nproc) + 1)
 script:
 - mkdir build
 - cd build
 - ../scripts/ci/org.centos/stream/8/x86_64/configure
 - make -j"$JOBS"
 - make NINJA=":" check
 - ../scripts/ci/org.centos/stream/8/x86_64/test-avocado
