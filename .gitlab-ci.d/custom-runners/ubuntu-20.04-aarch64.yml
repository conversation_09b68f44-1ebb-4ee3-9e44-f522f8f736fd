# All ubuntu-20.04 jobs should run successfully in an environment
# setup by the scripts/ci/setup/qemu/build-environment.yml task
# "Install basic packages to build QEMU on Ubuntu 18.04/20.04"

ubuntu-20.04-aarch64-all-linux-static:
 needs: []
 stage: build
 tags:
 - ubuntu_20.04
 - aarch64
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
 - if: "$AARCH64_RUNNER_AVAILABLE"
 script:
 # --disable-libssh is needed because of https://bugs.launchpad.net/qemu/+bug/1838763
 # --disable-glusterfs is needed because there's no static version of those libs in distro supplied packages
 - mkdir build
 - cd build
 - ../configure --enable-debug --static --disable-system --disable-glusterfs --disable-libssh
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check V=1
 - make --output-sync -j`nproc` check-tcg V=1

ubuntu-20.04-aarch64-all:
 needs: []
 stage: build
 tags:
 - ubuntu_20.04
 - aarch64
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$AARCH64_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --disable-libssh
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check V=1

ubuntu-20.04-aarch64-alldbg:
 needs: []
 stage: build
 tags:
 - ubuntu_20.04
 - aarch64
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
 - if: "$AARCH64_RUNNER_AVAILABLE"
 script:
 - mkdir build
 - cd build
 - ../configure --enable-debug --disable-libssh
 - make clean
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check V=1

ubuntu-20.04-aarch64-clang:
 needs: []
 stage: build
 tags:
 - ubuntu_20.04
 - aarch64
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$AARCH64_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --disable-libssh --cc=clang-10 --cxx=clang++-10 --enable-sanitizers
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check V=1

ubuntu-20.04-aarch64-tci:
 needs: []
 stage: build
 tags:
 - ubuntu_20.04
 - aarch64
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$AARCH64_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --disable-libssh --enable-tcg-interpreter
 - make --output-sync -j`nproc`

ubuntu-20.04-aarch64-notcg:
 needs: []
 stage: build
 tags:
 - ubuntu_20.04
 - aarch64
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$AARCH64_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --disable-libssh --disable-tcg
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check V=1
