@CIRRUS_VM_INSTANCE_TYPE@:
  @CIRRUS_VM_IMAGE_SELECTOR@: @CIRRUS_VM_IMAGE_NAME@
  cpu: @CIRRUS_VM_CPUS@
  memory: @CIRRUS_VM_RAM@

env:
  CIRRUS_CLONE_DEPTH: 1
  CI_REPOSITORY_URL: "@CI_REPOSITORY_URL@"
  CI_COMMIT_REF_NAME: "@CI_COMMIT_REF_NAME@"
  CI_COMMIT_SHA: "@CI_COMMIT_SHA@"
  PATH: "@PATH@"
  PKG_CONFIG_PATH: "@PKG_CONFIG_PATH@"
  PYTHON: "@PYTHON@"
  MAKE: "@MAKE@"
  CONFIGURE_ARGS: "@CONFIGURE_ARGS@"
  TEST_TARGETS: "@TEST_TARGETS@"

build_task:
  install_script:
    - @UPDATE_COMMAND@
    - @INSTALL_COMMAND@ @PKGS@
    - if test -n "@PYPI_PKGS@" ; then @PIP3@ install @PYPI_PKGS@ ; fi
  clone_script:
    - git clone --depth 100 "$CI_REPOSITORY_URL" .
    - git fetch origin "$CI_COMMIT_REF_NAME"
    - git reset --hard "$CI_COMMIT_SHA"
  build_script:
    - mkdir build
    - cd build
    - ../configure --enable-werror $CONFIGURE_ARGS
      || { cat config.log meson-logs/meson-log.txt; exit 1; }
    - $MAKE -j$(sysctl -n hw.ncpu)
    - for TARGET in $TEST_TARGETS ;
      do
        $MAKE -j$(sysctl -n hw.ncpu) $TARGET V=1 ;
      done
