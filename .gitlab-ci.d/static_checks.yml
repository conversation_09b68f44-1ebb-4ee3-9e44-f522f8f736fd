check-patch:
  stage: build
  image: $CI_REGISTRY_IMAGE/qemu/centos8:latest
  needs:
    job: amd64-centos8-container
  script:
    - .gitlab-ci.d/check-patch.py
  variables:
    GIT_DEPTH: 1000
  rules:
    - if: '$CI_PROJECT_NAMESPACE == "qemu-project"'
      when: never
    - when: on_success
      allow_failure: true

check-dco:
  stage: build
  image: $CI_REGISTRY_IMAGE/qemu/centos8:latest
  needs:
    job: amd64-centos8-container
  script: .gitlab-ci.d/check-dco.py
  variables:
    GIT_DEPTH: 1000
  rules:
    - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
      when: never
    - when: on_success

check-python-pipenv:
  stage: test
  image: $CI_REGISTRY_IMAGE/qemu/python:latest
  script:
    - make -C python check-pipenv
  variables:
    GIT_DEPTH: 1
  needs:
    job: python-container

check-python-tox:
  stage: test
  image: $CI_REGISTRY_IMAGE/qemu/python:latest
  script:
    - make -C python check-tox
  variables:
    GIT_DEPTH: 1
    QEMU_TOX_EXTRA_ARGS: --skip-missing-interpreters=false
  needs:
    job: python-container
  allow_failure: true
